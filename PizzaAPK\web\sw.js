// Service Worker pour Pizza Caisse Manager PWA
const CACHE_NAME = 'pizza-caisse-v1.1.0';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/assets/icon.png',
  '/assets/adaptive-icon.png',
  '/assets/favicon.png'
];

// Installation du Service Worker
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Installation');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('📦 Cache ouvert');
        return cache.addAll(urlsToCache);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', (event) => {
  console.log('✅ Service Worker: Activation');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('🗑️ Suppression ancien cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Interception des requêtes
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Retourner la ressource du cache si disponible
        if (response) {
          return response;
        }
        // Sinon, récupérer depuis le réseau
        return fetch(event.request);
      }
    )
  );
});

// Gestion des notifications push (optionnel)
self.addEventListener('push', (event) => {
  console.log('📱 Notification push reçue');
  
  const options = {
    body: 'Nouvelle commande reçue !',
    icon: '/assets/icon.png',
    badge: '/assets/favicon.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    }
  };
  
  event.waitUntil(
    self.registration.showNotification('Pizza Caisse Manager', options)
  );
});

// Gestion des clics sur notifications
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Clic sur notification');
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow('/')
  );
});
