{"expo": {"name": "Pizza Caisse Manager", "slug": "pizza-caisse-manager", "version": "1.1.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#e74c3c"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.pizzacaissemanager", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#e74c3c"}, "package": "com.pizzacaissemanager", "versionCode": 3, "edgeToEdgeEnabled": true, "permissions": ["WRITE_EXTERNAL_STORAGE", "READ_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro", "name": "Pizza Caisse Manager", "shortName": "Pizza Caisse", "lang": "fr", "scope": "/", "themeColor": "#e74c3c", "backgroundColor": "#e74c3c", "display": "standalone", "orientation": "portrait", "startUrl": "/", "preferRelatedApplications": false}, "plugins": [["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}]], "extra": {"eas": {"projectId": "aff316d9-d121-40ba-a2dc-b4afd7195694"}}, "owner": "brona", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/aff316d9-d121-40ba-a2dc-b4afd7195694"}}}