import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';
import { PizzaListScreen } from './src/screens/PizzaListScreen';
import { OrderFormScreen } from './src/screens/OrderFormScreen';
import { StatisticsScreen } from './src/screens/StatisticsScreen';
import { LoginScreen } from './src/screens/LoginScreen';
import { InventoryScreen } from './src/screens/InventoryScreen';
import PWAInstallButton from './src/components/PWAInstallButton';
import InstallPrompt from './src/components/InstallPrompt';

type Screen = 'PizzaList' | 'OrderForm' | 'Statistics' | 'Inventory';

function App(): React.JSX.Element {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<Screen>('PizzaList');
  const [orderData, setOrderData] = useState<any>(null);

  const navigate = (screen: Screen, data?: any) => {
    if (data) setOrderData(data);
    setCurrentScreen(screen);
    // Force re-render pour s'assurer que la navigation fonctionne
    setTimeout(() => {
      setCurrentScreen(screen);
    }, 100);
  };

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentScreen('PizzaList');
  };

  if (!isLoggedIn) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#2c3e50" />
        <LoginScreen onLogin={handleLogin} />
      </View>
    );
  }

  const renderScreen = () => {
    const mockNavigation = {
      navigate,
      goBack: () => setCurrentScreen('PizzaList'),
      logout: handleLogout,
    };

    switch (currentScreen) {
      case 'PizzaList':
        return <PizzaListScreen navigation={mockNavigation} />;
      case 'OrderForm':
        return <OrderFormScreen navigation={mockNavigation} route={{ params: orderData }} />;
      case 'Statistics':
        return <StatisticsScreen navigation={mockNavigation} />;
      case 'Inventory':
        return <InventoryScreen navigation={mockNavigation} />;
      default:
        return <PizzaListScreen navigation={mockNavigation} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2c3e50" />
      {renderScreen()}

      {/* Bouton d'installation PWA */}
      <PWAInstallButton />

      {/* Prompt d'installation */}
      <InstallPrompt />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
