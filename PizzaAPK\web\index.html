<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="utf-8" />
  <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  
  <!-- PWA Meta Tags -->
  <meta name="application-name" content="Pizza Caisse Manager" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Pizza Caisse" />
  <meta name="description" content="Application de gestion de pizzeria avec commandes, statistiques et stocks" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="theme-color" content="#e74c3c" />
  
  <!-- Icons -->
  <link rel="icon" type="image/png" sizes="48x48" href="/assets/favicon.png" />
  <link rel="icon" type="image/png" sizes="192x192" href="/assets/icon.png" />
  <link rel="icon" type="image/png" sizes="512x512" href="/assets/icon.png" />
  <link rel="apple-touch-icon" href="/assets/icon.png" />
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json" />
  
  <title>Pizza Caisse Manager</title>
  
  <style>
    /* Styles pour l'écran de chargement */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #e74c3c, #c0392b);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: white;
      font-family: Arial, sans-serif;
    }
    
    .pizza-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      animation: bounce 2s infinite;
    }
    
    .app-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
    
    .app-subtitle {
      font-size: 1rem;
      opacity: 0.8;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255,255,255,0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-top: 2rem;
    }
    
    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Masquer le loading une fois l'app chargée */
    .app-loaded #loading-screen {
      display: none;
    }
  </style>
</head>

<body>
  <!-- Écran de chargement -->
  <div id="loading-screen">
    <div class="pizza-icon">🍕</div>
    <div class="app-title">Pizza Caisse Manager</div>
    <div class="app-subtitle">Chargement de votre application...</div>
    <div class="loading-spinner"></div>
  </div>
  
  <!-- Container principal de l'app -->
  <div id="root"></div>
  
  <!-- Installation PWA Script -->
  <script>
    // Variables globales pour PWA
    let deferredPrompt;
    let isInstalled = false;
    
    // Vérifier si l'app est déjà installée
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
      isInstalled = true;
      console.log('📱 App déjà installée en mode standalone');
    }
    
    // Écouter l'événement beforeinstallprompt
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('💾 Prompt d\'installation PWA disponible');
      e.preventDefault();
      deferredPrompt = e;
      
      // Afficher un bouton d'installation personnalisé
      showInstallButton();
    });
    
    // Fonction pour afficher le bouton d'installation
    function showInstallButton() {
      if (isInstalled) return;
      
      const installButton = document.createElement('button');
      installButton.innerHTML = '📱 Installer l\'App';
      installButton.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #e74c3c;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1000;
        font-size: 14px;
      `;
      
      installButton.addEventListener('click', installApp);
      document.body.appendChild(installButton);
      
      // Masquer le bouton après 10 secondes
      setTimeout(() => {
        if (installButton.parentNode) {
          installButton.remove();
        }
      }, 10000);
    }
    
    // Fonction d'installation
    async function installApp() {
      if (!deferredPrompt) return;
      
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('✅ App installée avec succès');
        isInstalled = true;
      } else {
        console.log('❌ Installation annulée');
      }
      
      deferredPrompt = null;
    }
    
    // Enregistrer le Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('✅ Service Worker enregistré:', registration.scope);
          })
          .catch((error) => {
            console.log('❌ Erreur Service Worker:', error);
          });
      });
    }
    
    // Masquer l'écran de chargement une fois l'app prête
    window.addEventListener('load', () => {
      setTimeout(() => {
        document.body.classList.add('app-loaded');
      }, 2000);
    });
    
    // Gestion des notifications push (optionnel)
    function requestNotificationPermission() {
      if ('Notification' in window) {
        Notification.requestPermission().then((permission) => {
          if (permission === 'granted') {
            console.log('✅ Notifications autorisées');
          }
        });
      }
    }
    
    // Demander les permissions après 5 secondes
    setTimeout(requestNotificationPermission, 5000);
  </script>
</body>
</html>
