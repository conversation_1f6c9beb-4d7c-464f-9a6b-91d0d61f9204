import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
  }
}

const PWAInstallButton: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallButton, setShowInstallButton] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Vérifier si on est sur le web
    if (Platform.OS !== 'web') return;

    // Vérifier si l'app est déjà installée
    const checkIfInstalled = () => {
      if (typeof window !== 'undefined') {
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        const isNavigatorStandalone = (window.navigator as any)?.standalone;
        const isInstalled = isStandalone || isNavigatorStandalone;
        setIsInstalled(isInstalled);
        
        if (isInstalled) {
          console.log('📱 App déjà installée en mode standalone');
        }
      }
    };

    checkIfInstalled();

    // Écouter l'événement beforeinstallprompt
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      console.log('💾 Prompt d\'installation PWA disponible');
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstallButton(true);
    };

    // Écouter l'événement appinstalled
    const handleAppInstalled = () => {
      console.log('✅ App installée avec succès');
      setIsInstalled(true);
      setShowInstallButton(false);
      setDeferredPrompt(null);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.addEventListener('appinstalled', handleAppInstalled);
    }

    // Afficher le bouton après 3 secondes si pas d'événement
    const fallbackTimer = setTimeout(() => {
      if (!isInstalled && !deferredPrompt) {
        setShowInstallButton(true);
      }
    }, 3000);

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
        window.removeEventListener('appinstalled', handleAppInstalled);
      }
      clearTimeout(fallbackTimer);
    };
  }, [isInstalled, deferredPrompt]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      // Fallback pour les navigateurs qui ne supportent pas l'installation automatique
      showManualInstallInstructions();
      return;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('✅ Installation acceptée');
        setIsInstalled(true);
      } else {
        console.log('❌ Installation refusée');
      }
      
      setDeferredPrompt(null);
      setShowInstallButton(false);
    } catch (error) {
      console.error('Erreur lors de l\'installation:', error);
      showManualInstallInstructions();
    }
  };

  const showManualInstallInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      instructions = 'Chrome: Cliquez sur les 3 points → "Installer Pizza Caisse Manager"';
    } else if (userAgent.includes('firefox')) {
      instructions = 'Firefox: Cliquez sur l\'icône + dans la barre d\'adresse';
    } else if (userAgent.includes('safari')) {
      instructions = 'Safari: Cliquez sur Partager → "Ajouter à l\'écran d\'accueil"';
    } else if (userAgent.includes('edg')) {
      instructions = 'Edge: Cliquez sur les 3 points → "Installer cette application"';
    } else {
      instructions = 'Recherchez l\'option "Installer" ou "Ajouter à l\'écran d\'accueil" dans le menu de votre navigateur';
    }

    alert(`📱 Installation manuelle:\n\n${instructions}`);
  };

  // Ne pas afficher si déjà installé ou pas sur web
  if (Platform.OS !== 'web' || isInstalled || !showInstallButton) {
    return null;
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.installButton} onPress={handleInstallClick}>
        <Text style={styles.installIcon}>📱</Text>
        <Text style={styles.installText}>Installer l'App</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 1000,
  },
  installButton: {
    backgroundColor: '#e74c3c',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  installIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  installText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PWAInstallButton;
