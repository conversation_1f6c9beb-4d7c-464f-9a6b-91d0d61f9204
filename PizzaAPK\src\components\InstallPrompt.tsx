import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, Alert } from 'react-native';

const InstallPrompt: React.FC = () => {
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // Afficher le prompt seulement sur web
    if (Platform.OS === 'web') {
      // Vérifier si l'app n'est pas déjà installée
      const isStandalone = typeof window !== 'undefined' && 
        (window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any)?.standalone);
      
      if (!isStandalone) {
        setShowPrompt(true);
      }
    }
  }, []);

  const handleInstallClick = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      instructions = `📱 Installation sur Chrome:

1. C<PERSON>z sur les 3 points (⋮) en haut à droite
2. Sélectionnez "Installer Pizza Caisse Manager"
3. Confirmez l'installation
4. L'app apparaîtra sur votre bureau/écran d'accueil !`;
    } else if (userAgent.includes('firefox')) {
      instructions = `📱 Installation sur Firefox:

1. Cliquez sur l'icône + dans la barre d'adresse
2. Ou allez dans le menu → "Installer cette application"
3. Confirmez l'installation`;
    } else if (userAgent.includes('safari')) {
      instructions = `📱 Installation sur Safari:

1. Cliquez sur le bouton Partager (carré avec flèche)
2. Sélectionnez "Ajouter à l'écran d'accueil"
3. Confirmez en appuyant sur "Ajouter"
4. L'icône pizza apparaîtra sur votre écran d'accueil !`;
    } else if (userAgent.includes('edg')) {
      instructions = `📱 Installation sur Edge:

1. Cliquez sur les 3 points (...) en haut à droite
2. Sélectionnez "Applications" → "Installer cette application"
3. Confirmez l'installation`;
    } else {
      instructions = `📱 Installation:

Recherchez l'option "Installer" ou "Ajouter à l'écran d'accueil" dans le menu de votre navigateur.

L'application fonctionnera alors comme une vraie app native !`;
    }

    Alert.alert(
      '📱 Installer Pizza Caisse Manager',
      instructions,
      [
        { text: 'Plus tard', style: 'cancel' },
        { text: 'Compris !', onPress: () => setShowPrompt(false) }
      ]
    );
  };

  const handleDismiss = () => {
    setShowPrompt(false);
  };

  if (Platform.OS !== 'web' || !showPrompt) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.promptCard}>
        <Text style={styles.icon}>🍕</Text>
        <Text style={styles.title}>Installer l'Application</Text>
        <Text style={styles.description}>
          Installez Pizza Caisse Manager pour une expérience optimale !
        </Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.dismissButton} onPress={handleDismiss}>
            <Text style={styles.dismissText}>Plus tard</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.installButton} onPress={handleInstallClick}>
            <Text style={styles.installText}>📱 Installer</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    padding: 20,
  },
  promptCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  icon: {
    fontSize: 48,
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  dismissButton: {
    flex: 1,
    backgroundColor: '#ecf0f1',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  dismissText: {
    color: '#7f8c8d',
    fontSize: 16,
    fontWeight: '600',
  },
  installButton: {
    flex: 1,
    backgroundColor: '#e74c3c',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  installText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default InstallPrompt;
